/* eslint-disable */
//prettier-ignore
module.exports = {
name: "@yarnpkg/plugin-engines",
factory: function (require) {
var plugin=(()=>{var l=Object.create,c=Object.defineProperty;var v=Object.getOwnPropertyDescriptor;var u=Object.getOwnPropertyNames;var m=Object.getPrototypeOf,h=Object.prototype.hasOwnProperty;var y=e=>c(e,"__esModule",{value:!0});var t=e=>{if(typeof require!="undefined")return require(e);throw new Error('Dynamic require of "'+e+'" is not supported')};var g=(e,o)=>{for(var r in o)c(e,r,{get:o[r],enumerable:!0})},P=(e,o,r)=>{if(o&&typeof o=="object"||typeof o=="function")for(let s of u(o))!h.call(e,s)&&s!=="default"&&c(e,s,{get:()=>o[s],enumerable:!(r=v(o,s))||r.enumerable});return e},i=e=>P(y(c(e!=null?l(m(e)):{},"default",e&&e.__esModule&&"default"in e?{get:()=>e.default,enumerable:!0}:{value:e,enumerable:!0})),e);var k={};g(k,{default:()=>$});var n=i(t("@yarnpkg/core")),p=i(t("@yarnpkg/fslib")),d=i(t("fs")),f=i(t("path")),a=i(t("semver")),N={hooks:{validateProject:e=>{let o=(0,d.readFileSync)((0,f.resolve)(p.npath.fromPortablePath(e.cwd),"package.json"),"utf-8"),{engines:r={}}=JSON.parse(o);if(r.node!=null&&!(0,a.satisfies)(process.version,r.node))throw new n.ReportError(n.MessageName.UNNAMED,`The current Node version ${process.version} does not satisfy the required version ${r.node}.`);if(r.yarn!=null&&!(0,a.satisfies)(n.YarnVersion,r.yarn))throw new n.ReportError(n.MessageName.UNNAMED,`The current Yarn version v${n.YarnVersion} does not satisfy the required version ${r.yarn}.`)},setupScriptEnvironment:async e=>{let o=(0,d.readFileSync)((0,f.resolve)(p.npath.fromPortablePath(e.cwd),"package.json"),"utf-8"),{engines:r={}}=JSON.parse(o);r.node!=null&&!(0,a.satisfies)(process.version,r.node)&&(console.error(`The current Node version ${process.version} does not satisfy the required version ${r.node}.`),process.exit(1)),r.yarn!=null&&!(0,a.satisfies)(n.YarnVersion,r.yarn)&&(console.error(`The current Yarn version v${n.YarnVersion} does not satisfy the required version ${r.yarn}.`),process.exit(1))}}},$=N;return k;})();
return plugin;
}
};
