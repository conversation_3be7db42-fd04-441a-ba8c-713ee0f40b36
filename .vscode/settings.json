{"eslint.workingDirectories": [{"pattern": "packages/*", "changeProcessCWD": true}, {"pattern": "apps/*", "changeProcessCWD": true}, {"pattern": "ee/apps/*", "changeProcessCWD": true}, {"pattern": "ee/packages/*", "changeProcessCWD": true}], "typescript.tsdk": "./node_modules/typescript/lib", "cSpell.words": ["autotranslate", "ciphertext", "Contextualbar", "fname", "Gazzodown", "katex", "listbox", "livechat", "<PERSON><PERSON><PERSON><PERSON>", "omnichannel", "photoswipe", "proxify", "searchbox", "tmid", "tshow"]}