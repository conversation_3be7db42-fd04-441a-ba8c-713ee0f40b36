{"kind": "Template", "apiVersion": "v1", "metadata": {"name": "rocket-chat", "annotations": {"description": "Rocket.Chat with a MongoDB database running with a Persistent storage. Use this template if you want to run Rocket.Chat in production.", "tags": "quickstart,nodejs,mongodb,instant-app", "iconClass": "icon-nodejs"}}, "objects": [{"kind": "PersistentVolumeClaim", "apiVersion": "v1", "metadata": {"name": "${DATABASE_SERVICE_NAME}"}, "spec": {"accessModes": ["ReadWriteOnce"], "resources": {"requests": {"storage": "${VOLUME_CAPACITY}"}}}}, {"kind": "DeploymentConfig", "apiVersion": "v1", "metadata": {"name": "mongodb", "labels": {"template": "mongodb-persistent-template"}}, "spec": {"strategy": {"type": "Recreate", "recreateParams": {"timeoutSeconds": 600}}, "triggers": [{"type": "ImageChange", "imageChangeParams": {"automatic": true, "containerNames": ["mongodb"], "from": {"kind": "ImageStreamTag", "namespace": "openshift", "name": "mongodb:latest"}}}, {"type": "ConfigChange"}], "replicas": 1, "test": false, "selector": {"name": "mongodb"}, "template": {"metadata": {"creationTimestamp": null, "labels": {"name": "mongodb"}}, "spec": {"containers": [{"name": "mongodb", "image": "registry.access.redhat.com/rhscl/mongodb-26-rhel7:latest", "ports": [{"containerPort": 27017, "protocol": "TCP"}], "env": [{"name": "MONGODB_USER", "value": "${MONGODB_USER}"}, {"name": "MONGODB_PASSWORD", "value": "${MONGODB_PASSWORD}"}, {"name": "MONGODB_DATABASE", "value": "${MONGODB_DATABASE}"}, {"name": "MONGODB_ADMIN_PASSWORD", "value": "${MONGODB_ADMIN_PASSWORD}"}], "resources": {"limits": {"memory": "${MEMORY_LIMIT}"}}, "volumeMounts": [{"name": "mongodb-data", "mountPath": "/var/lib/mongodb/data"}], "livenessProbe": {"tcpSocket": {"port": 27017}, "initialDelaySeconds": 30, "timeoutSeconds": 1, "periodSeconds": 10, "successThreshold": 1, "failureThreshold": 3}, "readinessProbe": {"exec": {"command": ["/bin/sh", "-i", "-c", "mongo 127.0.0.1:27017/$MONGODB_DATABASE -u $MONGODB_USER -p $MONGODB_PASSWORD --eval=\"quit()\""]}, "initialDelaySeconds": 3, "timeoutSeconds": 1, "periodSeconds": 10, "successThreshold": 1, "failureThreshold": 3}, "terminationMessagePath": "/dev/termination-log", "imagePullPolicy": "IfNotPresent", "securityContext": {"capabilities": {}, "privileged": false}}], "volumes": [{"name": "${DATABASE_SERVICE_NAME}-data", "persistentVolumeClaim": {"claimName": "${DATABASE_SERVICE_NAME}"}}], "restartPolicy": "Always", "terminationGracePeriodSeconds": 30, "dnsPolicy": "ClusterFirst", "securityContext": {}}}}, "status": {}}, {"kind": "ImageStream", "apiVersion": "v1", "metadata": {"name": "rocketchat"}, "spec": {"dockerImageRepository": "${ROCKETCHAT_IMAGE}", "tags": [{"name": "latest", "annotations": {"description": "Provides a Rocket.Chat application", "iconClass": "icon-nodejs", "tags": "rocketchat"}, "from": {"kind": "ImageStreamTag", "name": "latest"}, "generation": 1, "importPolicy": {}}]}}, {"kind": "DeploymentConfig", "apiVersion": "v1", "metadata": {"name": "rocketchat", "creationTimestamp": null, "labels": {"app": "rocketchat"}, "annotations": {"openshift.io/generated-by": "OpenShiftNewApp"}}, "spec": {"strategy": {"type": "Rolling", "rollingParams": {"updatePeriodSeconds": 1, "intervalSeconds": 1, "timeoutSeconds": 600, "maxUnavailable": "25%", "maxSurge": "25%"}, "resources": {}}, "triggers": [{"type": "ConfigChange"}, {"type": "ImageChange", "imageChangeParams": {"automatic": true, "containerNames": ["rocketchat"], "from": {"kind": "ImageStreamTag", "name": "rocketchat:latest"}}}], "replicas": 1, "test": false, "selector": {"app": "rocketchat", "deploymentconfig": "rocketchat"}, "template": {"metadata": {"creationTimestamp": null, "labels": {"app": "rocketchat", "deploymentconfig": "rocketchat"}, "annotations": {"openshift.io/container.rocketchat.image.entrypoint": "[\"node\",\"main.js\"]", "openshift.io/generated-by": "OpenShiftNewApp"}}, "spec": {"volumes": [{"name": "rocketchat-volume-1", "emptyDir": {}}], "containers": [{"name": "rocketchat", "image": "${ROCKETCHAT_IMAGE}:latest", "ports": [{"containerPort": 3000, "protocol": "TCP"}], "env": [{"name": "MONGO_URL", "value": "mongodb://${MONGODB_USER}:${MONGODB_PASSWORD}@mongodb:27017/${MONGODB_DATABASE}"}], "resources": {}, "volumeMounts": [{"name": "rocketchat-volume-1", "mountPath": "/app/uploads"}], "readinessProbe": {"httpGet": {"path": "/api/v1/info", "port": 3000, "scheme": "HTTP"}, "initialDelaySeconds": 5, "timeoutSeconds": 1, "periodSeconds": 10, "successThreshold": 1, "failureThreshold": 3}, "livenessProbe": {"httpGet": {"path": "/api/v1/info", "port": 3000, "scheme": "HTTP"}, "initialDelaySeconds": 30, "timeoutSeconds": 1, "periodSeconds": 10, "successThreshold": 1, "failureThreshold": 3}, "terminationMessagePath": "/dev/termination-log", "imagePullPolicy": "Always"}], "restartPolicy": "Always", "terminationGracePeriodSeconds": 30, "dnsPolicy": "ClusterFirst", "securityContext": {}}}}, "status": {}}, {"kind": "Route", "apiVersion": "v1", "metadata": {"name": "rocketchat", "creationTimestamp": null, "labels": {"app": "rocketchat"}, "annotations": {"openshift.io/host.generated": "true"}}, "spec": {"to": {"kind": "Service", "name": "rocketchat"}, "port": {"targetPort": "3000-tcp"}}, "status": {"ingress": null}}, {"kind": "Service", "apiVersion": "v1", "metadata": {"name": "mongodb", "creationTimestamp": null, "labels": {"template": "mongodb-persistent-template"}}, "spec": {"ports": [{"name": "mongo", "protocol": "TCP", "port": 27017, "targetPort": 27017}], "selector": {"name": "mongodb"}, "type": "ClusterIP", "sessionAffinity": "None"}, "status": {"loadBalancer": {}}}, {"kind": "Service", "apiVersion": "v1", "metadata": {"name": "rocketchat", "creationTimestamp": null, "labels": {"app": "rocketchat"}, "annotations": {"openshift.io/generated-by": "OpenShiftNewApp"}}, "spec": {"ports": [{"name": "3000-tcp", "protocol": "TCP", "port": 3000, "targetPort": 3000}], "selector": {"app": "rocketchat", "deploymentconfig": "rocketchat"}, "type": "ClusterIP", "sessionAffinity": "None"}, "status": {"loadBalancer": {}}}], "parameters": [{"name": "MEMORY_LIMIT", "displayName": "Memory Limit", "description": "Maximum amount of memory the container can use.", "value": "512Mi"}, {"name": "DATABASE_SERVICE_NAME", "displayName": "Database Service Name", "description": "The name of the OpenShift Service exposed for the database.", "value": "mongodb", "required": true}, {"name": "MONGODB_USER", "displayName": "MongoDB User", "description": "Username for MongoDB user that will be used for accessing the database.", "generate": "expression", "from": "user[A-Z0-9]{3}", "required": true}, {"name": "MONGODB_PASSWORD", "displayName": "MongoDB Password", "description": "Password for the MongoDB user.", "generate": "expression", "from": "[a-zA-Z0-9]{16}", "required": true}, {"name": "MONGODB_DATABASE", "displayName": "MongoDB Database Name", "description": "Name of the MongoDB database accessed.", "value": "rocketchatdb", "required": true}, {"name": "MONGODB_ADMIN_PASSWORD", "displayName": "MongoDB Admin Password", "description": "Password for the database admin user.", "generate": "expression", "from": "[a-zA-Z0-9]{16}", "required": true}, {"name": "ROCKETCHAT_IMAGE", "displayName": "RocketChat Image", "description": "The RocketChat image to use for this deployment", "required": true, "value": "rocketchat/rocket.chat"}, {"name": "VOLUME_CAPACITY", "displayName": "Volume Capacity", "description": "Volume space available for data, e.g. 512Mi, 2Gi.", "value": "1Gi", "required": true}]}