import './helpers/composeRoomWithLastMessage';
import './helpers/getLoggedInUser';
import './helpers/getPaginationItems';
import './helpers/getUserFromParams';
import './helpers/getUserInfo';
import './helpers/isUserFromParams';
import './helpers/parseJsonQuery';
import './default/info';
import './v1/assets';
import './v1/calendar';
import './v1/channels';
import './v1/chat';
import './v1/cloud';
import './v1/commands';
import './v1/dns';
import './v1/e2e';
import './v1/emoji-custom';
import './v1/groups';
import './v1/im';
import './v1/integrations';
import './v1/invites';
import './v1/import';
import './v1/ldap';
import './v1/misc';
import './v1/permissions';
import './v1/presence';
import './v1/push';
import './v1/roles';
import './v1/rooms';
import './v1/settings';
import './v1/stats';
import './v1/subscriptions';
import './v1/users';
import './v1/videoConference';
import './v1/autotranslate';
import './v1/webdav';
import './v1/oauthapps';
import './v1/custom-sounds';
import './v1/custom-user-status';
import './v1/instances';
import './v1/banners';
import './v1/email-inbox';
import './v1/mailer';
import './v1/teams';
import './v1/voip/extensions';
import './v1/voip/queues';
import './v1/voip/omnichannel';
import './v1/voip';
import './v1/federation';
import './v1/moderation';

// This has to come last so all endpoints are registered before generating the OpenAPI documentation
import './default/openApi';

export { API, defaultRateLimiterOptions } from './api';
