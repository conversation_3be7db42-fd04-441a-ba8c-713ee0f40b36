#!/bin/bash

echo """
[49m[K[0m
[22C[48;5;203m               [49m
[20C[48;5;203m                      [49m
[20C[48;5;203m                      [49m
[15C[48;5;203m          [48;5;231m               [48;5;203m       [49m
[10C[48;5;203m          [48;5;231m               [48;5;16m     [48;5;231m  [48;5;203m        [5C[48;5;16m     [49m
[2C[48;5;203m               [48;5;231m               [48;5;16m   [48;5;188m     [48;5;16m  [48;5;231m   [48;5;203m       [48;5;16m        [49m
[5C[48;5;203m          [48;5;231m                 [48;5;16m   [48;5;188m  [48;5;231m     [48;5;16m   [48;5;231m  [48;5;203m     [48;5;16m   [48;5;231m     [48;5;16m  [49m
[5C[48;5;203m          [48;5;231m                 [48;5;16m   [48;5;188m  [48;5;231m     [48;5;16m   [48;5;231m  [48;5;203m     [48;5;16m   [48;5;231m     [48;5;16m  [49m
[10C[48;5;203m     [48;5;231m     [48;5;203m  [48;5;231m        [48;5;203m  [48;5;16m   [48;5;188m  [48;5;231m        [48;5;16m       [48;5;231m        [48;5;16m  [49m
[10C[48;5;203m     [48;5;231m  [48;5;203m        [48;5;231m  [48;5;203m     [48;5;16m   [48;5;188m  [48;5;231m                       [48;5;16m  [49m
[10C[48;5;203m     [48;5;231m     [48;5;203m  [48;5;231m        [48;5;203m  [48;5;16m   [48;5;188m  [48;5;231m                       [48;5;16m  [49m
[5C[48;5;203m          [48;5;231m               [48;5;16m  [48;5;188m     [48;5;231m     [48;5;16m   [48;5;231m       [48;5;16m   [48;5;231m       [48;5;16m   [49m
[2C[48;5;203m               [48;5;231m             [48;5;16m  [48;5;188m   [48;5;231m     [48;5;16m     [48;5;231m     [48;5;16m     [48;5;231m       [48;5;16m   [49m
[2C[48;5;203m               [48;5;231m             [48;5;16m  [48;5;188m   [48;5;231m     [48;5;16m     [48;5;231m     [48;5;16m     [48;5;231m       [48;5;16m   [49m
[7C[48;5;16m     [48;5;203m        [48;5;231m          [48;5;16m  [48;5;188m   [48;5;231m                           [48;5;16m   [49m
[5C[48;5;16m  [48;5;231m     [48;5;203m                  [48;5;16m  [48;5;188m   [48;5;231m     [48;5;16m  [48;5;231m     [48;5;16m   [48;5;231m  [48;5;16m   [48;5;231m       [48;5;16m   [49m
[5C[48;5;16m  [48;5;231m   [48;5;16m     [48;5;231m  [48;5;203m        [48;5;16m       [48;5;188m     [48;5;231m   [48;5;16m               [48;5;231m     [48;5;16m  [49m
[5C[48;5;16m     [5C  [48;5;231m     [48;5;16m   [7C   [48;5;188m  [48;5;231m                    [48;5;16m   [49m
[5C[48;5;16m     [5C  [48;5;231m     [48;5;16m   [7C   [48;5;188m  [48;5;231m                    [48;5;16m   [49m
[15C[48;5;16m  [48;5;231m   [48;5;16m     [10C                      [49m
[15C[48;5;16m       [13C  [48;5;231m     [48;5;16m   [2C   [48;5;231m     [48;5;16m  [49m
[37C[48;5;16m        [2C        [49m



[0m
"""

echo """
██████╗  ██████╗  ██████╗██╗  ██╗███████╗████████╗ ██████╗██╗  ██╗ █████╗ ████████╗    ██████╗ ██████╗ ███████╗██╗   ██╗██╗███████╗██╗    ██╗
██╔══██╗██╔═══██╗██╔════╝██║ ██╔╝██╔════╝╚══██╔══╝██╔════╝██║  ██║██╔══██╗╚══██╔══╝    ██╔══██╗██╔══██╗██╔════╝██║   ██║██║██╔════╝██║    ██║
██████╔╝██║   ██║██║     █████╔╝ █████╗     ██║   ██║     ███████║███████║   ██║       ██████╔╝██████╔╝█████╗  ██║   ██║██║█████╗  ██║ █╗ ██║
██╔══██╗██║   ██║██║     ██╔═██╗ ██╔══╝     ██║   ██║     ██╔══██║██╔══██║   ██║       ██╔═══╝ ██╔══██╗██╔══╝  ╚██╗ ██╔╝██║██╔══╝  ██║███╗██║
██║  ██║╚██████╔╝╚██████╗██║  ██╗███████╗   ██║██╗╚██████╗██║  ██║██║  ██║   ██║       ██║     ██║  ██║███████╗ ╚████╔╝ ██║███████╗╚███╔███╔╝
╚═╝  ╚═╝ ╚═════╝  ╚═════╝╚═╝  ╚═╝╚══════╝   ╚═╝╚═╝ ╚═════╝╚═╝  ╚═╝╚═╝  ╚═╝   ╚═╝       ╚═╝     ╚═╝  ╚═╝╚══════╝  ╚═══╝  ╚═╝╚══════╝ ╚══╝╚══╝
"""

mongod --fork --replSet rs0 --config /etc/mongod.conf

until mongo --eval "db" &> /dev/null; do
	echo "MongoDB still not ready, sleeping"
	sleep 1
done

sleep 2

# initiate mongo replica set
for i in `seq 1 30`; do
	mongo rocketchat --eval "
	rs.initiate({
		_id: 'rs0',
		members: [ { _id: 0, host: 'localhost:27017' } ]})" &&
	s=$? && break || s=$?;
	echo "Tried $i times. Waiting 5 secs...";
	sleep 5;
done;

sleep 2

# try multiple times until replica set is ready
for i in `seq 1 30`; do
	node main.js &&
	s=$? && break || s=$?;
	echo "Tried $i times. Waiting 5 secs...";
	sleep 5;
done;
