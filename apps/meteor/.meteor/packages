# Meteor packages used by this project, one per line.

#

# 'meteor add' and 'meteor remove' will edit this file for you,

# but you can also edit it by hand.

rocketchat:ddp
rocketchat:mongo-config
rocketchat:livechat
rocketchat:streamer
rocketchat:version
rocketchat:user-presence

accounts-base@3.1.1
accounts-facebook@1.3.4
accounts-github@1.5.1
accounts-google@1.4.1
accounts-meteor-developer@1.5.1
accounts-oauth@1.4.6
accounts-password@3.2.0
accounts-twitter@1.5.2

google-oauth@1.4.5
oauth@3.0.2
oauth2@1.3.3

check@1.4.4
ddp-rate-limiter@1.2.2
rate-limit@1.1.2
email@3.1.2

meteor-base@1.5.2
ddp-common@1.4.4
webapp@2.0.7

mongo@2.1.2

reload@1.3.2
service-configuration@1.3.5
session@1.2.2
shell-server@0.6.1

dispatch:run-as-user
ostrio:cookies


meteorhacks:inject-initial

routepolicy@1.1.2

webapp-hashing@1.1.2
facts-base@1.0.2

tracker@1.3.4
reactive-dict@1.3.2
reactive-var@1.0.13

babel-compiler@7.12.0
standard-minifier-css@1.9.3
dynamic-import@0.7.4
ecmascript@0.16.11
typescript@5.6.4

autoupdate@2.0.1

# photoswipe

zodern:types
zodern:standard-minifier-js
ostrio:flow-router-extra
