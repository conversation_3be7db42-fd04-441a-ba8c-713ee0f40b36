tasks:
  - init: |
      nvm install $(jq -r .engines.node package.json) &&
      curl https://install.meteor.com/?release=$(
        curl -so- https://raw.githubusercontent.com/RocketChat/Rocket.Chat/develop/apps/meteor/.meteor/release | cut -d@ -f2
      ) | sh &&
      export PATH=$PATH:/home/<USER>/.meteor &&
      yarn &&
      export ROOT_URL=$(gp url 3000)
    command: yarn dsv

ports:
  - port: 3000
    visibility: public
    onOpen: open-preview 

vscode:
  extensions:
    - esbenp.prettier-vscode