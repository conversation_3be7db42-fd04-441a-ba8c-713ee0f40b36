/packages/* @RocketChat/Architecture
/packages/apps-engine/ @RocketChat/apps
/packages/core-typings/ @RocketChat/Architecture
/packages/rest-typings/ @RocketChat/Architecture @RocketChat/backend
/packages/ui-contexts/ @RocketChat/frontend
/packages/eslint-config/ @RocketChat/Architecture
/packages/livechat/ @RocketChat/frontend @RocketChat/Architecture
/.vscode/ @RocketChat/Architecture
/.github/ @RocketChat/Architecture
/_templates/ @RocketChat/Architecture
/apps/meteor/client/ @RocketChat/frontend
/apps/meteor/tests/e2e @RocketChat/frontend
/apps/meteor/tests/end-to-end @RocketChat/backend
/apps/meteor/tests/unit/app @RocketChat/backend
/apps/meteor/tests/unit/app/ui-utils @RocketChat/frontend
/apps/meteor/tests/unit/client @RocketChat/frontend
/apps/meteor/tests/unit/server @RocketChat/backend
/apps/meteor/app/apps/ @RocketChat/apps
/apps/meteor/app/livechat @RocketChat/omnichannel
/apps/meteor/app/voip @RocketChat/omnichannel
/apps/meteor/app/sms @RocketChat/omnichannel
/apps/meteor/server @RocketChat/backend
/packages/models @RocketChat/Architecture
apps/meteor/server/startup/migrations @RocketChat/Architecture
/apps/meteor/packages/rocketchat-livechat @RocketChat/omnichannel
/apps/meteor/server/services/voip-asterisk @RocketChat/omnichannel
/apps/meteor/server/services/omnichannel-voip @RocketChat/omnichannel
/apps/meteor/server/features/EmailInbox @RocketChat/omnichannel
/apps/meteor/ee/app/canned-responses @RocketChat/omnichannel
/apps/meteor/ee/app/livechat @RocketChat/omnichannel
/apps/meteor/ee/app/livechat-enterprise @RocketChat/omnichannel
/apps/meteor/client/omnichannel @RocketChat/omnichannel
/apps/meteor/client/components/omnichannel @RocketChat/omnichannel
/apps/meteor/client/components/voip @RocketChat/omnichannel
