---
name: Bug report
about: Create a report to help us improve

---

<!-- 

Please see our guide for opening issues: https://developer.rocket.chat/rocket.chat/contribute-to-rocket.chat/ways-to-contribute/report-bugs

If you have questions or are looking for help/support please see: https://rocket.chat/docs/getting-support

If you are experiencing a bug please search our issues to be sure it is not already present: https://github.com/RocketChat/Rocket.Chat/issues

-->

### Description:

<!-- A clear and concise description of what the bug is. -->

### Steps to reproduce:

1. <!-- Go to '...' -->
2. <!-- Click on '....' -->
3. <!-- and so on... -->

### Expected behavior:

<!-- What you expect to happen -->

### Actual behavior:

<!-- What actually happens with SCREENSHOT, if applicable -->

### Server Setup Information:

- Version of Rocket.Chat Server: 
- License Type:
- Number of Users: 
- Operating System: 
- Deployment Method: <!-- snap/docker/tar/etc -->
- Number of Running Instances: 
- DB Replicaset Oplog: 
- NodeJS Version: 
- MongoDB Version:

### Client Setup Information

- Desktop App or Browser Version:
- Operating System:

### Additional context

<!-- Add any other context about the problem here. -->

### Relevant logs:

<!-- Logs from both SERVER and BROWSER -->
<!-- For more information about collecting logs please see: https://rocket.chat/docs/contributing/reporting-issues#gathering-logs -->
