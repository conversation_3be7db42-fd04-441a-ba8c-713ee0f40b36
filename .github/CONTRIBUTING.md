# Contributing to Rocket.Chat

**First off, thanks for taking the time to contribute! :tada::+1:**

> There are many ways to contribute to Rocket.Chat even if you're not technical or a developer:
>
> * Email <NAME_EMAIL> to tell us how much you love the project
> * Write about us in your blogs
> * Fix some small typos in our [documentation](https://docs.rocket.chat/contributing)
> * Become our [GitHub sponsor](https://github.com/sponsors/RocketChat)
> * Tell others about us and help us spread the word
>
> Every bit of contribution is appreciated 🙂 thank you!

The following is a set of guidelines for contributing to Rocket.Chat, which are hosted in the [Rocket.Chat Organization](https://github.com/RocketChat) on GitHub.

__Note:__ If there's a feature you'd like, there's a bug you'd like to fix, or you'd just like to get involved please raise an issue and start a conversation. We'll help as much as we can so you can get contributing - although we may not always be able to respond right away :)

## Development Guidelines

Check out our Handbook for the [Development Guidelines](https://handbook.rocket.chat/product/development/development-guidelines) on how to set up your environment, do code, test and push your code. There you find our patterns on how to compose your Pull Requests' titles to have your contribution accepted.

## Contributor License Agreement

To have your contribution accepted you must sign our [Contributor License Agreement](https://cla-assistant.io/RocketChat/Rocket.Chat). In case you submit a Pull Request before signing the CLA GitHub will alert you with a new comment asking you to sign and will block the Pull Request from being merged by us.

Please review and sign our CLA at https://cla-assistant.io/RocketChat/Rocket.Chat
