version: 2
updates:
  # Main repo NPM package
  - package-ecosystem: npm
    directory: '/'
    schedule:
      interval: daily
    open-pull-requests-limit: 10
    labels:
      - 'automerge'

  ## EE dependencies
  - package-ecosystem: npm
    directory: '/ee/server/services'
    schedule:
      interval: daily
    open-pull-requests-limit: 10
    labels:
      - 'automerge'

  # Maintain dependencies for GitHub Actions
  - package-ecosystem: 'github-actions'
    directory: '/'
    schedule:
      interval: 'daily'
