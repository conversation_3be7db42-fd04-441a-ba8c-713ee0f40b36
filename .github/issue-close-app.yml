# Config for Close Issue app: https://probot.github.io/apps/close-issue/

# Comment that will be sent if an issue is judged to be closed
comment: |
  This issue was closed because it does not use our bug report issue template.

  Please make sure to use it and fill it as much as you can so we can provide better and faster support.

  The following sections **must not** be removed, or else the BOT will close it immediately again:

    * Steps to reproduce
    * Expected behavior
    * Actual behavior
    * Server Setup Information
    * Version of Rocket.Chat Server
issueConfigs:
# There can be several configs for different kind of issues.
- content:
# Bug report
  - "Steps to reproduce"
  - "Expected behavior"
  - "Actual behavior"
  - "Server Setup Information"
  - "Version of Rocket.Chat Server"
- content:
# Release issue
  - "Before Release - Preparation"
# The issue is considered to be valid if it includes all keywords from any of these two configs.
# Or it will be closed by the app.
