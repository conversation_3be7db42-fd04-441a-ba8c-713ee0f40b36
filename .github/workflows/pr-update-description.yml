name: 'Release PR Description'

on:
  pull_request:
    branches:
      - master

concurrency: ${{ github.workflow }}-${{ github.ref }}

jobs:
  update-pr:
    runs-on: ubuntu-24.04
    if: startsWith(github.head_ref, 'release-')
    steps:
      - name: Checkout <PERSON><PERSON>
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.CI_PAT }}

      - name: Setup NodeJS
        uses: ./.github/actions/setup-node
        with:
          node-version: 22.16.0
          deno-version: 1.43.5
          cache-modules: true
          install: true
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

      - uses: rharkor/caching-for-turbo@v1.8

      - name: Build packages
        run: yarn build

      - name: Update PR description
        uses: ./packages/release-action
        with:
          action: update-pr-description
        env:
          GITHUB_TOKEN: ${{ secrets.CI_PAT }}
