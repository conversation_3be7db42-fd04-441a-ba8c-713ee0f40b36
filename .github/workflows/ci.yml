name: CI

on:
  release:
    types: [published]
  pull_request:
    branches: '**'
    paths-ignore:
      - '**.md'
  push:
    branches:
      - develop
    paths-ignore:
      - '**.md'

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

env:
  TOOL_NODE_FLAGS: ${{ vars.TOOL_NODE_FLAGS }}

jobs:
  release-versions:
    name: ⚙️ Variables Setup
    runs-on: ubuntu-24.04
    outputs:
      release: ${{ steps.by-tag.outputs.release }}
      latest-release: ${{ steps.latest.outputs.latest-release }}
      docker-tag: ${{ steps.docker.outputs.docker-tag }}
      gh-docker-tag: ${{ steps.docker.outputs.gh-docker-tag }}
      lowercase-repo: ${{ steps.var.outputs.lowercase-repo }}
      rc-dockerfile: '${{ github.workspace }}/apps/meteor/.docker/Dockerfile'
      rc-docker-tag: '${{ steps.docker.outputs.gh-docker-tag }}'
      node-version: ${{ steps.var.outputs.node-version }}
      deno-version: ${{ steps.var.outputs.deno-version }}
      official-platform: 'alpine'
      # this is 100% intentional, secrets are not available for forks, so ee-tests will always fail
      # to avoid this, we are using a dummy license, expiring at 2026-07-01
      enterprise-license: Uo7Jcr6WW0XYA8ydHd+Sk6pZ9/0V6dIASnyTwvUrNym/zJg2Ma3eYNKkC8osXLCc72y1ahohnWY7/+7IYkvono3GYXQR+IGvYbbrVgNR6OjMahd9P/odHZL1GFTm2qHrEL5Hh/XEOG+YluFeRdWPzCizQlp4zGGOi0+PkQo096TR9NVCLrsErVl2MW1WM6ZM1W5EUJG9pKly4BQnaOTUAlor1im6i8qPTDCKrISZfLiZEWuQKaPW/GE3mRKjQNjDh0CabX1N2S880pRRGoozBYAnp2NmFfrQW0+5ihKisBTIeMbMZ7K5NE5PkYU1nhQDcc+rpDHtwG9Ceg5X0J+oea3UfrPTmDON2aSI0iO22kvL6G7QI3fyrEIvJrMbxcNKxAFeQYgnjisw/b06+chWSG4jG686Fx58XrVS87dFhWL9WoGltsk1dJCntUQvI1sX6zOfpvyg1iWRnHfYDOrwoWlX57XMm29fWineEoqnOOTOVnA/uP+DKEhercQ9Xuo7Cr6zJxpQpwd03e7ODVjiEbTDqlkZE687rmxRCD4Wmu8L86WIl2xSEIajKLX301Ww5mz/FdLqk+Mg32lkW66W3azQKvJ1440NBrYxhpJ+dl9vSFMb3s1+xnz1cYUbjUcq9mARvORcgy5mLwKulmqT6Sq0Uvbv10YCO0TW0beXYW8=
    steps:
      - uses: actions/checkout@v4
        with:
          sparse-checkout: |
            package.json
            .tool-versions
          sparse-checkout-cone-mode: false
          ref: ${{ github.ref }}

      - id: var
        run: |
          LOWERCASE_REPOSITORY=$(echo "${{ github.repository_owner }}" | tr "[:upper:]" "[:lower:]")

          echo "LOWERCASE_REPOSITORY: ${LOWERCASE_REPOSITORY}"
          echo "lowercase-repo=${LOWERCASE_REPOSITORY}" >> $GITHUB_OUTPUT

          NODE_VERSION=$(node -p "require('./package.json').engines.node")
          echo "NODE_VERSION: ${NODE_VERSION}"
          echo "node-version=${NODE_VERSION}" >> $GITHUB_OUTPUT

          DENO_VERSION=$(awk '$1=="deno"{ print $2 }' .tool-versions)
          echo "DENO_VERSION: ${DENO_VERSION}"
          echo "deno-version=${DENO_VERSION}" >> $GITHUB_OUTPUT

      - id: by-tag
        run: |
          if echo "$GITHUB_REF_NAME" | grep -Eq '^[0-9]+\.[0-9]+\.[0-9]+$' ; then
            RELEASE="latest"
          elif echo "$GITHUB_REF_NAME" | grep -Eq '^[0-9]+\.[0-9]+\.[0-9]+-rc\.[0-9]+$' ; then
            RELEASE="release-candidate"
          fi
          echo "RELEASE: ${RELEASE}"
          echo "release=${RELEASE}" >> $GITHUB_OUTPUT

      - id: latest
        run: |
          LATEST_RELEASE="$(
            git -c 'versionsort.suffix=-' ls-remote -t --exit-code --refs --sort=-v:refname "https://github.com/$GITHUB_REPOSITORY" '*' |
              awk -F/ '$NF !~ /rc|beta/ { print $NF; exit }'
          )"
          echo "LATEST_RELEASE: ${LATEST_RELEASE}"
          echo "latest-release=${LATEST_RELEASE}" >> $GITHUB_OUTPUT

      - id: docker
        run: |
          if [[ '${{ github.event_name }}' == 'pull_request' ]]; then
            DOCKER_TAG="pr-${{ github.event.number }}"
          else
            DOCKER_TAG="gh-${{ github.run_id }}"
          fi
          echo "DOCKER_TAG: ${DOCKER_TAG}"
          echo "gh-docker-tag=${DOCKER_TAG}" >> $GITHUB_OUTPUT

  notify-draft-services:
    name: 🚀 Notify external services - draft
    runs-on: ubuntu-24.04
    needs: [release-versions]
    steps:
      - uses: actions/checkout@v4
        with:
          sparse-checkout: |
            package.json
          sparse-checkout-cone-mode: false
          ref: ${{ github.ref }}

      - name: Register release on cloud as Draft
        if: github.event_name == 'release'
        env:
          UPDATE_TOKEN: ${{ secrets.UPDATE_TOKEN }}
        run: |
          REPO_VERSION=$(node -p "require('./package.json').version")

          if [[ '${{ github.event_name }}' = 'release' ]]; then
            GIT_TAG="${GITHUB_REF#*tags/}"
            GIT_BRANCH=""
            ARTIFACT_NAME="${REPO_VERSION}"
            RC_VERSION=$GIT_TAG

            if [[ '${{ needs.release-versions.outputs.release }}' = 'release-candidate' ]]; then
              RC_RELEASE=candidate
            elif [[ '${{ needs.release-versions.outputs.release }}' = 'latest' ]]; then
              RC_RELEASE=stable
            fi
          else
            GIT_TAG=""
            GIT_BRANCH="${GITHUB_REF#*heads/}"
            ARTIFACT_NAME="${REPO_VERSION}.$GITHUB_SHA"
            RC_VERSION="${REPO_VERSION}"
            RC_RELEASE=develop
          fi;

          curl -H "Content-Type: application/json" -H "X-Update-Token: $UPDATE_TOKEN" -d \
              "{\"nodeVersion\": \"${{ needs.release-versions.outputs.node-version }}\", \"denoVersion\": \"${{ needs.release-versions.outputs.deno-version }}\", \"compatibleMongoVersions\": [\"5.0\", \"6.0\", \"7.0\"], \"commit\": \"$GITHUB_SHA\", \"tag\": \"$RC_VERSION\", \"branch\": \"$GIT_BRANCH\", \"artifactName\": \"$ARTIFACT_NAME\", \"releaseType\": \"draft\", \"draftAs\": \"$RC_RELEASE\"}" \
              https://releases.rocket.chat/update

  packages-build:
    name: 📦 Build Packages
    needs: [release-versions, notify-draft-services]
    runs-on: ubuntu-24.04
    steps:
      - name: Github Info
        run: |
          echo "GITHUB_ACTION: $GITHUB_ACTION"
          echo "GITHUB_ACTOR: $GITHUB_ACTOR"
          echo "GITHUB_REF: $GITHUB_REF"
          echo "GITHUB_HEAD_REF: $GITHUB_HEAD_REF"
          echo "GITHUB_BASE_REF: $GITHUB_BASE_REF"
          echo "github.event_name: ${{ github.event_name }}"
          cat $GITHUB_EVENT_PATH

      - name: Set Swap Space
        uses: pierotofy/set-swap-space@master
        with:
          swap-size-gb: 4

      - uses: actions/checkout@v4

      - name: Setup NodeJS
        uses: ./.github/actions/setup-node
        with:
          node-version: ${{ needs.release-versions.outputs.node-version }}
          deno-version: ${{ needs.release-versions.outputs.deno-version }}
          cache-modules: true
          install: true
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
          HARDENED_MODE: '1'

      - name: Cache vite
        uses: actions/cache@v4
        with:
          path: ./node_modules/.vite
          key: vite-local-cache-${{ runner.OS }}-${{ hashFiles('package.json') }}
          restore-keys: |
            vite-local-cache-${{ runner.os }}-

      - uses: rharkor/caching-for-turbo@v1.8

      - name: Build Rocket.Chat Packages
        run: yarn build

      - name: Store turbo build
        uses: actions/upload-artifact@v4
        with:
          name: turbo-build
          path: .turbo/cache
          overwrite: true
          include-hidden-files: true

  deploy-preview:
    runs-on: ubuntu-24.04
    needs: [release-versions, packages-build]
    steps:
      - uses: actions/checkout@v4

      - uses: rharkor/caching-for-turbo@v1.8
        if: github.event.action != 'closed'

      - name: Setup NodeJS
        uses: ./.github/actions/setup-node
        if: github.event.action != 'closed'
        with:
          node-version: 22.16.0
          deno-version: 1.43.5
          cache-modules: true
          install: true
      - name: Restore turbo build
        uses: actions/download-artifact@v4
        with:
          name: turbo-build
          path: .turbo/cache
      - name: Build
        if: github.event.action != 'closed'
        run: |
          yarn turbo run build-preview
          yarn turbo run .:build-preview-move
          npx indexifier .preview --html  --extensions .html  > .preview/index.html

      - uses: rossjrw/pr-preview-action@v1
        with:
          source-dir: .preview
          preview-branch: gh-pages
          umbrella-dir: pr-preview
          action: auto

  build:
    name: 📦 Meteor Build - coverage
    needs: [release-versions, packages-build]
    runs-on: ubuntu-24.04

    steps:
      - name: Collect Workflow Telemetry
        uses: catchpoint/workflow-telemetry-action@v2
        with:
          theme: dark
          job_summary: true
          comment_on_pr: false
      - name: Github Info
        run: |
          echo "GITHUB_ACTION: $GITHUB_ACTION"
          echo "GITHUB_ACTOR: $GITHUB_ACTOR"
          echo "GITHUB_REF: $GITHUB_REF"
          echo "GITHUB_HEAD_REF: $GITHUB_HEAD_REF"
          echo "GITHUB_BASE_REF: $GITHUB_BASE_REF"
          echo "github.event_name: ${{ github.event_name }}"
          cat $GITHUB_EVENT_PATH

      - uses: actions/checkout@v4

      - uses: ./.github/actions/meteor-build
        with:
          node-version: ${{ needs.release-versions.outputs.node-version }}
          deno-version: ${{ needs.release-versions.outputs.deno-version }}
          coverage: true

  build-prod:
    name: 📦 Meteor Build - official
    needs: [tests-done, release-versions, packages-build]
    if: (github.event_name == 'release' || github.ref == 'refs/heads/develop')
    runs-on: ubuntu-24.04

    steps:
      - name: Collect Workflow Telemetry
        uses: catchpoint/workflow-telemetry-action@v2
        with:
          theme: dark
          job_summary: true
          comment_on_pr: false
      - name: Github Info
        run: |
          echo "GITHUB_ACTION: $GITHUB_ACTION"
          echo "GITHUB_ACTOR: $GITHUB_ACTOR"
          echo "GITHUB_REF: $GITHUB_REF"
          echo "GITHUB_HEAD_REF: $GITHUB_HEAD_REF"
          echo "GITHUB_BASE_REF: $GITHUB_BASE_REF"
          echo "github.event_name: ${{ github.event_name }}"
          cat $GITHUB_EVENT_PATH

      - uses: actions/checkout@v4

      - uses: ./.github/actions/meteor-build
        with:
          node-version: ${{ needs.release-versions.outputs.node-version }}
          deno-version: ${{ needs.release-versions.outputs.deno-version }}
          coverage: false

  # TODO: this should go away once upstream builds are fixed
  build-matrix-rust-bindings-for-alpine:
    name: Builds matrix rust bindings against alpine
    runs-on: ubuntu-24.04
    steps:
      - name: check cache for matrix-rust-sdk-crypto-nodejs
        id: matrix-rust-sdk-crypto-nodejs
        uses: actions/cache@v4
        with:
          path: /tmp/build/matrix-sdk-crypto.linux-x64-musl.node
          key: matrix-rust-sdk-crypto-nodejs-v0.2.0-beta.1

      - uses: actions/checkout@v4
        with:
          repository: matrix-org/matrix-rust-sdk-crypto-nodejs
          ref: v0.2.0-beta.1 # https://github.com/element-hq/matrix-bot-sdk/blob/e72a4c498e00c6c339a791630c45d00a351f56a8/package.json#L58

      - if: steps.matrix-rust-sdk-crypto-nodejs.outputs.cache-hit != 'true'
        run: sudo apt-get install -y musl-tools libunwind-dev && find /usr/include -name stdarg.h 2>/dev/null || true

      - if: steps.matrix-rust-sdk-crypto-nodejs.outputs.cache-hit != 'true'
        uses: actions/setup-node@v4.4.0
        with:
          node-version: 22.16.0

      - if: steps.matrix-rust-sdk-crypto-nodejs.outputs.cache-hit != 'true'
        uses: actions-rust-lang/setup-rust-toolchain@v1
        with:
          toolchain: '1.76'
          target: x86_64-unknown-linux-musl

      - if: steps.matrix-rust-sdk-crypto-nodejs.outputs.cache-hit != 'true'
        name: Install ziglang
        uses: mlugg/setup-zig@v1
        with:
          version: 0.13.0

      - if: steps.matrix-rust-sdk-crypto-nodejs.outputs.cache-hit != 'true'
        name: Build
        run: |
          npm install --ignore-scripts
          npx napi build --release --target x86_64-unknown-linux-musl --platform --zig
          mkdir -p /tmp/build
          mv matrix-sdk-crypto.linux-x64-musl.node /tmp/build/matrix-sdk-crypto.linux-x64-musl.node

  build-gh-docker-coverage:
    name: 🚢 Build Docker Images for Testing
    needs: [build, release-versions, build-matrix-rust-bindings-for-alpine]
    runs-on: ubuntu-24.04

    env:
      RC_DOCKERFILE: ${{ needs.release-versions.outputs.rc-dockerfile }}.${{ matrix.platform }}
      RC_DOCKER_TAG: ${{ needs.release-versions.outputs.rc-docker-tag }}.${{ matrix.platform }}
      DOCKER_TAG: ${{ needs.release-versions.outputs.gh-docker-tag }}
      LOWERCASE_REPOSITORY: ${{ needs.release-versions.outputs.lowercase-repo }}

    strategy:
      fail-fast: false
      matrix:
        platform: ['alpine']

    steps:
      - uses: actions/checkout@v4

      # we only build and publish the actual docker images if not a PR from a fork
      - uses: ./.github/actions/build-docker
        if: (github.event.pull_request.head.repo.full_name == github.repository || github.event_name == 'release' || github.ref == 'refs/heads/develop') && github.actor != 'dependabot[bot]'
        with:
          CR_USER: ${{ secrets.CR_USER }}
          CR_PAT: ${{ secrets.CR_PAT }}
          node-version: ${{ needs.release-versions.outputs.node-version }}
          deno-version: ${{ needs.release-versions.outputs.deno-version }}
          platform: ${{ matrix.platform }}
          build-containers: ${{ matrix.platform == needs.release-versions.outputs.official-platform && 'authorization-service account-service ddp-streamer-service presence-service stream-hub-service queue-worker-service omnichannel-transcript-service' || '' }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: Make sure matrix bindings load
        if: (github.event.pull_request.head.repo.full_name == github.repository || github.event_name == 'release' || github.ref == 'refs/heads/develop') && matrix.platform == 'alpine' && github.actor != 'dependabot[bot]'
        run: |
          docker run --rm -w /app/bundle/programs/server/npm/node_modules/matrix-appservice-bridge ghcr.io/rocketchat/rocket.chat:$RC_DOCKER_TAG -e 'require(".")'

      - name: Rename official Docker tag to GitHub Container Registry
        if: matrix.platform == needs.release-versions.outputs.official-platform && (github.event.pull_request.head.repo.full_name == github.repository || github.event_name == 'release' || github.ref == 'refs/heads/develop') && github.actor != 'dependabot[bot]'
        run: |
          IMAGE_NAME_BASE="ghcr.io/${LOWERCASE_REPOSITORY}/rocket.chat:${DOCKER_TAG}"

          echo "Push Docker image: ${IMAGE_NAME_BASE}.official"
          docker tag ${IMAGE_NAME_BASE}.${{matrix.platform}} ${IMAGE_NAME_BASE}.official
          docker push ${IMAGE_NAME_BASE}.official

  build-gh-docker:
    name: 🚢 Build Docker Images for Production
    needs: [build-prod, release-versions]
    runs-on: ubuntu-24.04

    env:
      RC_DOCKERFILE: ${{ needs.release-versions.outputs.rc-dockerfile }}.${{ matrix.platform }}
      RC_DOCKER_TAG: ${{ needs.release-versions.outputs.rc-docker-tag }}.${{ matrix.platform }}
      DOCKER_TAG: ${{ needs.release-versions.outputs.gh-docker-tag }}
      LOWERCASE_REPOSITORY: ${{ needs.release-versions.outputs.lowercase-repo }}

    strategy:
      fail-fast: false
      matrix:
        platform: ['alpine']

    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/actions/build-docker
        with:
          CR_USER: ${{ secrets.CR_USER }}
          CR_PAT: ${{ secrets.CR_PAT }}
          node-version: ${{ needs.release-versions.outputs.node-version }}
          deno-version: ${{ needs.release-versions.outputs.deno-version }}
          platform: ${{ matrix.platform }}
          build-containers: ${{ matrix.platform == needs.release-versions.outputs.official-platform && 'authorization-service account-service ddp-streamer-service presence-service stream-hub-service queue-worker-service omnichannel-transcript-service' || '' }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: Rename official Docker tag to GitHub Container Registry
        if: matrix.platform == needs.release-versions.outputs.official-platform
        run: |
          IMAGE_NAME_BASE="ghcr.io/${LOWERCASE_REPOSITORY}/rocket.chat:${DOCKER_TAG}"

          echo "Push Docker image: ${IMAGE_NAME_BASE}"
          docker tag ${IMAGE_NAME_BASE}.${{matrix.platform}} $IMAGE_NAME_BASE
          docker push $IMAGE_NAME_BASE

          echo "Push Docker image: ${IMAGE_NAME_BASE}.official"
          docker tag ${IMAGE_NAME_BASE}.${{matrix.platform}} ${IMAGE_NAME_BASE}.official
          docker push ${IMAGE_NAME_BASE}.official

  checks:
    needs: [release-versions, packages-build]

    name: 🔎 Code Check
    uses: ./.github/workflows/ci-code-check.yml
    with:
      node-version: ${{ needs.release-versions.outputs.node-version }}
      deno-version: ${{ needs.release-versions.outputs.deno-version }}

  test-storybook:
    name: 🔨 Test Storybook
    needs: [packages-build, release-versions]

    uses: ./.github/workflows/ci-test-storybook.yml
    with:
      node-version: ${{ needs.release-versions.outputs.node-version }}
      deno-version: ${{ needs.release-versions.outputs.deno-version }}
    secrets:
      CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}

  test-unit:
    name: 🔨 Test Unit
    needs: [packages-build, release-versions]

    uses: ./.github/workflows/ci-test-unit.yml
    with:
      node-version: ${{ needs.release-versions.outputs.node-version }}
      deno-version: ${{ needs.release-versions.outputs.deno-version }}
      enterprise-license: ${{ needs.release-versions.outputs.enterprise-license }}
    secrets:
      CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}

  test-api:
    name: 🔨 Test API (CE)
    needs: [checks, build-gh-docker-coverage, release-versions]

    uses: ./.github/workflows/ci-test-e2e.yml
    with:
      type: api
      release: ce
      node-version: ${{ needs.release-versions.outputs.node-version }}
      deno-version: ${{ needs.release-versions.outputs.deno-version }}
      lowercase-repo: ${{ needs.release-versions.outputs.lowercase-repo }}
      rc-dockerfile: ${{ needs.release-versions.outputs.rc-dockerfile }}
      rc-docker-tag: ${{ needs.release-versions.outputs.rc-docker-tag }}
      gh-docker-tag: ${{ needs.release-versions.outputs.gh-docker-tag }}
    secrets:
      CR_USER: ${{ secrets.CR_USER }}
      CR_PAT: ${{ secrets.CR_PAT }}

  test-ui:
    name: 🔨 Test UI (CE)
    needs: [checks, build-gh-docker-coverage, release-versions]

    uses: ./.github/workflows/ci-test-e2e.yml
    with:
      type: ui
      release: ce
      transporter: 'nats://nats:4222'
      enterprise-license: ${{ needs.release-versions.outputs.enterprise-license }}
      shard: '[1, 2, 3, 4]'
      total-shard: 4
      node-version: ${{ needs.release-versions.outputs.node-version }}
      deno-version: ${{ needs.release-versions.outputs.deno-version }}
      lowercase-repo: ${{ needs.release-versions.outputs.lowercase-repo }}
      rc-dockerfile: ${{ needs.release-versions.outputs.rc-dockerfile }}
      rc-docker-tag: ${{ needs.release-versions.outputs.rc-docker-tag }}
      gh-docker-tag: ${{ needs.release-versions.outputs.gh-docker-tag }}
      retries: ${{ (github.event_name == 'release' || github.ref == 'refs/heads/develop' || github.ref == 'refs/heads/master') && 2 || 0 }}
    secrets:
      CR_USER: ${{ secrets.CR_USER }}
      CR_PAT: ${{ secrets.CR_PAT }}
      QASE_API_TOKEN: ${{ secrets.QASE_API_TOKEN }}
      REPORTER_ROCKETCHAT_API_KEY: ${{ secrets.REPORTER_ROCKETCHAT_API_KEY }}
      REPORTER_ROCKETCHAT_URL: ${{ secrets.REPORTER_ROCKETCHAT_URL }}
      REPORTER_JIRA_ROCKETCHAT_API_KEY: ${{ secrets.REPORTER_JIRA_ROCKETCHAT_API_KEY }}

  test-api-ee:
    name: 🔨 Test API (EE)
    needs: [checks, build-gh-docker-coverage, release-versions]

    uses: ./.github/workflows/ci-test-e2e.yml
    with:
      type: api
      release: ee
      transporter: 'nats://nats:4222'
      enterprise-license: ${{ needs.release-versions.outputs.enterprise-license }}
      mongodb-version: "['5.0']"
      node-version: ${{ needs.release-versions.outputs.node-version }}
      deno-version: ${{ needs.release-versions.outputs.deno-version }}
      lowercase-repo: ${{ needs.release-versions.outputs.lowercase-repo }}
      rc-dockerfile: ${{ needs.release-versions.outputs.rc-dockerfile }}
      rc-docker-tag: ${{ needs.release-versions.outputs.rc-docker-tag }}
      gh-docker-tag: ${{ needs.release-versions.outputs.gh-docker-tag }}
    secrets:
      CR_USER: ${{ secrets.CR_USER }}
      CR_PAT: ${{ secrets.CR_PAT }}

  test-ui-ee:
    name: 🔨 Test UI (EE)
    needs: [checks, build-gh-docker-coverage, release-versions]

    uses: ./.github/workflows/ci-test-e2e.yml
    with:
      type: ui
      release: ee
      transporter: 'nats://nats:4222'
      enterprise-license: ${{ needs.release-versions.outputs.enterprise-license }}
      shard: '[1, 2, 3, 4, 5]'
      total-shard: 5
      mongodb-version: "['5.0']"
      node-version: ${{ needs.release-versions.outputs.node-version }}
      deno-version: ${{ needs.release-versions.outputs.deno-version }}
      lowercase-repo: ${{ needs.release-versions.outputs.lowercase-repo }}
      rc-dockerfile: ${{ needs.release-versions.outputs.rc-dockerfile }}
      rc-docker-tag: ${{ needs.release-versions.outputs.rc-docker-tag }}
      gh-docker-tag: ${{ needs.release-versions.outputs.gh-docker-tag }}
      retries: ${{ (github.event_name == 'release' || github.ref == 'refs/heads/develop' || github.ref == 'refs/heads/master') && 2 || 0 }}
    secrets:
      CR_USER: ${{ secrets.CR_USER }}
      CR_PAT: ${{ secrets.CR_PAT }}
      QASE_API_TOKEN: ${{ secrets.QASE_API_TOKEN }}
      REPORTER_ROCKETCHAT_API_KEY: ${{ secrets.REPORTER_ROCKETCHAT_API_KEY }}
      REPORTER_ROCKETCHAT_URL: ${{ secrets.REPORTER_ROCKETCHAT_URL }}
      CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
      REPORTER_JIRA_ROCKETCHAT_API_KEY: ${{ secrets.REPORTER_JIRA_ROCKETCHAT_API_KEY }}

  test-ui-ee-watcher:
    name: 🔨 Test UI (EE)
    needs: [checks, build-gh-docker-coverage, release-versions]

    uses: ./.github/workflows/ci-test-e2e.yml
    with:
      type: ui
      release: ee
      transporter: 'nats://nats:4222'
      enterprise-license: ${{ needs.release-versions.outputs.enterprise-license }}
      shard: '[1, 2, 3, 4, 5]'
      total-shard: 5
      mongodb-version: "['7.0']"
      node-version: ${{ needs.release-versions.outputs.node-version }}
      deno-version: ${{ needs.release-versions.outputs.deno-version }}
      lowercase-repo: ${{ needs.release-versions.outputs.lowercase-repo }}
      rc-dockerfile: ${{ needs.release-versions.outputs.rc-dockerfile }}
      rc-docker-tag: ${{ needs.release-versions.outputs.rc-docker-tag }}
      gh-docker-tag: ${{ needs.release-versions.outputs.gh-docker-tag }}
      retries: ${{ (github.event_name == 'release' || github.ref == 'refs/heads/develop' || github.ref == 'refs/heads/master') && 2 || 0 }}
      db-watcher-disabled: 'false'
    secrets:
      CR_USER: ${{ secrets.CR_USER }}
      CR_PAT: ${{ secrets.CR_PAT }}
      QASE_API_TOKEN: ${{ secrets.QASE_API_TOKEN }}
      REPORTER_ROCKETCHAT_API_KEY: ${{ secrets.REPORTER_ROCKETCHAT_API_KEY }}
      REPORTER_ROCKETCHAT_URL: ${{ secrets.REPORTER_ROCKETCHAT_URL }}
      CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
      REPORTER_JIRA_ROCKETCHAT_API_KEY: ${{ secrets.REPORTER_JIRA_ROCKETCHAT_API_KEY }}

  tests-done:
    name: ✅ Tests Done
    runs-on: ubuntu-24.04
    needs: [checks, test-unit, test-api, test-ui, test-api-ee, test-ui-ee, test-ui-ee-watcher]
    if: always()
    steps:
      - name: Test finish aggregation
        run: |
          if [[ '${{ needs.checks.result }}' != 'success' ]]; then
            exit 1
          fi

          if [[ '${{ needs.test-unit.result }}' != 'success' ]]; then
            exit 1
          fi

          if [[ '${{ needs.test-api.result }}' != 'success' ]]; then
            exit 1
          fi

          if [[ '${{ needs.test-ui.result }}' != 'success' ]]; then
            exit 1
          fi

          if [[ '${{ needs.test-api-ee.result }}' != 'success' ]]; then
            exit 1
          fi

          if [[ '${{ needs.test-ui-ee.result }}' != 'success' ]]; then
            exit 1
          fi

          if [[ '${{ needs.test-ui-ee-watcher.result }}' != 'success' ]]; then
            exit 1
          fi

          echo finished

  deploy:
    name: 🚀 Publish build assets
    runs-on: ubuntu-24.04
    if: github.event_name == 'release' || github.ref == 'refs/heads/develop'
    needs: [build-gh-docker, release-versions]

    steps:
      - uses: actions/checkout@v4
        with:
          sparse-checkout: |
            package.json
          sparse-checkout-cone-mode: false
          ref: ${{ github.ref }}

      - name: Restore build
        uses: actions/download-artifact@v4
        with:
          name: build
          path: /tmp/build

      - name: Publish assets
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_DEFAULT_REGION: 'us-east-1'
          GPG_PASSWORD: ${{ secrets.GPG_PASSWORD }}
        run: |
          REPO_VERSION=$(node -p "require('./package.json').version")

          if [[ '${{ github.event_name }}' = 'release' ]]; then
            GIT_TAG="${GITHUB_REF#*tags/}"
            ARTIFACT_NAME="${REPO_VERSION}"
          else
            GIT_TAG=""
            ARTIFACT_NAME="${REPO_VERSION}.$GITHUB_SHA"
          fi;

          ROCKET_DEPLOY_DIR="/tmp/deploy"
          FILENAME="$ROCKET_DEPLOY_DIR/rocket.chat-$ARTIFACT_NAME.tgz";

          aws s3 cp s3://rocketchat/sign.key.gpg .github/sign.key.gpg

          mkdir -p $ROCKET_DEPLOY_DIR

          cp .github/sign.key.gpg /tmp
          gpg --yes --batch --passphrase=$GPG_PASSWORD /tmp/sign.key.gpg
          gpg --allow-secret-key-import --import /tmp/sign.key
          rm /tmp/sign.key

          ln -s /tmp/build/Rocket.Chat.tar.gz "$FILENAME"
          gpg --armor --detach-sign "$FILENAME"

          aws s3 cp $ROCKET_DEPLOY_DIR/ s3://download.rocket.chat/build/ --recursive

  docker-image-publish:
    name: 🚀 Publish Docker Image (main)
    runs-on: ubuntu-24.04
    needs: [deploy, release-versions]

    strategy:
      matrix:
        # this is currently a mix of variants and different images
        release: ['alpine']
    env:
      IMAGE_NAME: 'rocketchat/rocket.chat'

    steps:
      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USER }}
          password: ${{ secrets.DOCKER_PASS }}

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ secrets.CR_USER }}
          password: ${{ secrets.CR_PAT }}

      - name: Get Docker image name
        id: gh-docker
        run: |
          GH_IMAGE_NAME="ghcr.io/${{ needs.release-versions.outputs.lowercase-repo }}/rocket.chat:${{ needs.release-versions.outputs.gh-docker-tag }}.${{ matrix.release }}"

          echo "GH_IMAGE_NAME: $GH_IMAGE_NAME"

          echo "gh-image-name=${GH_IMAGE_NAME}" >> $GITHUB_OUTPUT

          DOCKER_TAG_SHA="sha-${GITHUB_SHA:0:7}"

          echo "DOCKER_TAG_SHA: ${DOCKER_TAG_SHA}"
          echo "gh-docker-tag-sha=${DOCKER_TAG_SHA}" >> $GITHUB_OUTPUT

      - name: Pull Docker image
        run: docker pull ${{ steps.gh-docker.outputs.gh-image-name }}

      - name: Publish Docker image
        run: |
          if [[ '${{ matrix.release }}' = 'preview' ]]; then
            IMAGE_NAME="${IMAGE_NAME}.preview"
          fi;

          # 'develop' or 'tag'
          DOCKER_TAG=$GITHUB_REF_NAME

          # append the variant name to docker tag
          if [[ '${{ matrix.release }}' = 'debian' ]]; then
            DOCKER_TAG="${DOCKER_TAG}-${{ matrix.release }}"
          fi;

          PUBLISHED_TAGS=()

          # tag and push the specific tag version
          docker tag ${{ steps.gh-docker.outputs.gh-image-name }} $IMAGE_NAME:$DOCKER_TAG
          docker push $IMAGE_NAME:$DOCKER_TAG

          PUBLISHED_TAGS+=("$IMAGE_NAME:$DOCKER_TAG")

          if [[ $GITHUB_REF == refs/tags/* ]]; then
            RELEASE="${{ needs.release-versions.outputs.release }}"

            if [[ '${{ matrix.release }}' = 'debian' ]]; then
              RELEASE="${RELEASE}-${{ matrix.release }}"
            fi;

            echo "RELEASE: $RELEASE"

            if [[ $RELEASE == 'latest' ]]; then
              if [[ '${{ needs.release-versions.outputs.latest-release }}' == $GITHUB_REF_NAME ]]; then
                docker tag ${{ steps.gh-docker.outputs.gh-image-name }} $IMAGE_NAME:$RELEASE
                docker push $IMAGE_NAME:$RELEASE

                PUBLISHED_TAGS+=("$IMAGE_NAME:$RELEASE")
              fi
            else
              docker tag ${{ steps.gh-docker.outputs.gh-image-name }} $IMAGE_NAME:$RELEASE
              docker push $IMAGE_NAME:$RELEASE

              PUBLISHED_TAGS+=("$IMAGE_NAME:$RELEASE")
            fi
          fi

          # commit hash
          TAG_SHA="${{ steps.gh-docker.outputs.gh-docker-tag-sha }}"

          # append the variant name to docker tag
          if [[ '${{ matrix.release }}' = 'debian' ]]; then
            TAG_SHA="${TAG_SHA}-${{ matrix.release }}"
          fi;

          docker tag ${{ steps.gh-docker.outputs.gh-image-name }} $IMAGE_NAME:$TAG_SHA
          docker push $IMAGE_NAME:$TAG_SHA

          PUBLISHED_TAGS+=("$IMAGE_NAME:$TAG_SHA")

          echo "::group::Published tags"

          for TAG in ${PUBLISHED_TAGS[@]}; do
            echo $TAG
          done

          echo "::endgroup::"

  services-docker-image-publish:
    name: 🚀 Publish Docker Image (services)
    runs-on: ubuntu-24.04
    needs: [deploy, release-versions]

    strategy:
      matrix:
        service: ['account', 'authorization', 'ddp-streamer', 'omnichannel-transcript', 'presence', 'queue-worker', 'stream-hub']

    steps:
      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USER }}
          password: ${{ secrets.DOCKER_PASS }}

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ secrets.CR_USER }}
          password: ${{ secrets.CR_PAT }}

      - name: Get Docker image name
        id: gh-docker
        run: |
          GH_IMAGE_NAME="ghcr.io/${{ needs.release-versions.outputs.lowercase-repo }}/${{ matrix.service }}-service:${{ needs.release-versions.outputs.gh-docker-tag }}"

          echo "GH_IMAGE_NAME: $GH_IMAGE_NAME"

          echo "gh-image-name=${GH_IMAGE_NAME}" >> $GITHUB_OUTPUT

          DOCKER_TAG_SHA="sha-${GITHUB_SHA:0:7}"

          echo "DOCKER_TAG_SHA: ${DOCKER_TAG_SHA}"
          echo "gh-docker-tag-sha=${DOCKER_TAG_SHA}" >> $GITHUB_OUTPUT

      - name: Pull Docker image
        run: docker pull ${{ steps.gh-docker.outputs.gh-image-name }}

      - name: Publish Docker images
        run: |
          DH_IMAGE_NAME="rocketchat/${{ matrix.service }}-service"

          # 'develop' or 'tag'
          DOCKER_TAG=$GITHUB_REF_NAME

          PUBLISHED_TAGS=()

          # tag and push the specific tag version
          docker tag ${{ steps.gh-docker.outputs.gh-image-name }} $DH_IMAGE_NAME:$DOCKER_TAG
          docker push $DH_IMAGE_NAME:$DOCKER_TAG

          PUBLISHED_TAGS+=("$DH_IMAGE_NAME:$DOCKER_TAG")

          if [[ $GITHUB_REF == refs/tags/* ]]; then
            RELEASE="${{ needs.release-versions.outputs.release }}"

            if [[ $RELEASE == 'latest' ]]; then
              if [[ '${{ needs.release-versions.outputs.latest-release }}' == $GITHUB_REF_NAME ]]; then
                docker tag ${{ steps.gh-docker.outputs.gh-image-name }} $DH_IMAGE_NAME:$RELEASE
                docker push $DH_IMAGE_NAME:$RELEASE

                PUBLISHED_TAGS+=("$DH_IMAGE_NAME:$RELEASE")
              fi
            else
              docker tag ${{ steps.gh-docker.outputs.gh-image-name }} $DH_IMAGE_NAME:$RELEASE
              docker push $DH_IMAGE_NAME:$RELEASE

              PUBLISHED_TAGS+=("$DH_IMAGE_NAME:$RELEASE")
            fi
          fi

          # commit hash
          TAG_SHA="${{ steps.gh-docker.outputs.gh-docker-tag-sha }}"

          docker tag ${{ steps.gh-docker.outputs.gh-image-name }} $DH_IMAGE_NAME:$TAG_SHA
          docker push $DH_IMAGE_NAME:$TAG_SHA

          PUBLISHED_TAGS+=("$DH_IMAGE_NAME:$TAG_SHA")

          echo "::group::Published tags"

          for TAG in ${PUBLISHED_TAGS[@]}; do
            echo $TAG
          done

          echo "::endgroup::"

  notify-services:
    name: 🚀 Notify external services
    runs-on: ubuntu-24.04
    needs:
      - services-docker-image-publish
      - docker-image-publish
      - release-versions
    steps:
      - uses: actions/checkout@v4
        with:
          sparse-checkout: |
            package.json
          sparse-checkout-cone-mode: false
          ref: ${{ github.ref }}

      - name: Releases service
        env:
          UPDATE_TOKEN: ${{ secrets.UPDATE_TOKEN }}
        run: |
          REPO_VERSION=$(node -p "require('./package.json').version")

          if [[ '${{ github.event_name }}' = 'release' ]]; then
            GIT_TAG="${GITHUB_REF#*tags/}"
            GIT_BRANCH=""
            ARTIFACT_NAME="${REPO_VERSION}"
            RC_VERSION=$GIT_TAG

            if [[ '${{ needs.release-versions.outputs.release }}' = 'release-candidate' ]]; then
              RC_RELEASE=candidate
            elif [[ '${{ needs.release-versions.outputs.release }}' = 'latest' ]]; then
              RC_RELEASE=stable
            fi
          else
            GIT_TAG=""
            GIT_BRANCH="${GITHUB_REF#*heads/}"
            ARTIFACT_NAME="${REPO_VERSION}.$GITHUB_SHA"
            RC_VERSION="${REPO_VERSION}"
            RC_RELEASE=develop
          fi;

          curl -H "Content-Type: application/json" -H "X-Update-Token: $UPDATE_TOKEN" -d \
              "{\"nodeVersion\": \"${{ needs.release-versions.outputs.node-version }}\", \"denoVersion\": \"${{ needs.release-versions.outputs.deno-version }}\", \"compatibleMongoVersions\": [\"5.0\", \"6.0\", \"7.0\"], \"commit\": \"$GITHUB_SHA\", \"tag\": \"$RC_VERSION\", \"branch\": \"$GIT_BRANCH\", \"artifactName\": \"$ARTIFACT_NAME\", \"releaseType\": \"$RC_RELEASE\"}" \
              https://releases.rocket.chat/update

          # Makes build fail if the release isn't there
          curl --fail https://releases.rocket.chat/$RC_VERSION/info

  docs-update:
    name: Update Version Durability

    if: github.event_name == 'release'
    needs:
      - services-docker-image-publish
      - docker-image-publish

    uses: ./.github/workflows/update-version-durability.yml
    secrets:
      CI_PAT: ${{ secrets.CI_PAT }}
      D360_TOKEN: ${{ secrets.D360_TOKEN }}
