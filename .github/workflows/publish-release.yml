name: Publish Final Release

on:
  push:
    branches:
      - master

concurrency: ${{ github.workflow }}-${{ github.ref }}

env:
  HUSKY: 0

jobs:
  release:
    name: Release
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.CI_PAT }}

      - name: Setup NodeJS
        uses: ./.github/actions/setup-node
        with:
          node-version: 22.16.0
          deno-version: 1.43.5
          cache-modules: true
          install: true
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

      - uses: rharkor/caching-for-turbo@v1.8

      - name: Build packages
        run: yarn build

      - name: Publish final release
        uses: ./packages/release-action
        with:
          action: publish-final
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
          GITHUB_TOKEN: ${{ secrets.CI_PAT }}
