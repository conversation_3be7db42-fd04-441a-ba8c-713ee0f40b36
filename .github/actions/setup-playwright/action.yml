name: Setup Playwright

runs:
  using: 'composite'

  steps:
    - name: <PERSON><PERSON> Playwright binaries
      uses: actions/cache@v4
      id: cache-playwright
      with:
        path: |
          ~/.cache/ms-playwright
        # This is the version of Play<PERSON> that we are using, if you are willing to upgrade, you should update this.
        key: playwright-1.52.0

    - name: Install Playwright
      shell: bash
      if: steps.cache-playwright.outputs.cache-hit != 'true'
      working-directory: .
      run: npx playwright install --with-deps
      