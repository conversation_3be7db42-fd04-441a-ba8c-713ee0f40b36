name: 'Setup Node'
description: 'Setup NodeJS'

inputs:
  node-version:
    required: true
    description: 'Node version'
  cache-modules:
    required: false
    description: 'Cache node_modules'
  install:
    required: false
    description: 'Install dependencies'
    type: boolean
  deno-version:
    required: true
    description: 'Deno version'
    type: string
  NPM_TOKEN:
    required: false
    description: 'NPM token'
  HARDENED_MODE:
    required: false
    description: 'Hardened mode'
    default: '0'

outputs:
  node-version:
    description: 'Node version'
    value: ${{ steps.node-version.outputs.node-version }}

runs:
  using: composite

  steps:
    - name: Cache Node Modules & Deno
      if: inputs.cache-modules
      id: cache-node-modules
      uses: actions/cache@v3
      with:
        # We need to cache node_modules for all workspaces with "hoistingLimits" defined
        path: |
          .turbo/cache
          node_modules
          apps/meteor/node_modules
          apps/meteor/ee/server/services/node_modules
          packages/apps-engine/node_modules
          packages/apps-engine/.deno-cache
        key: node-modules-${{ hashFiles('yarn.lock') }}-deno-v${{ inputs.deno-version }}-${{ hashFiles('packages/apps-engine/deno-runtime/deno.lock') }}
    #
    # Could use this command to list all paths to save:
    # find . -name 'node_modules' -prune | grep -v "/\.meteor/" | grep -v "/meteor/packages/"

    - name: Use Node.js ${{ inputs.node-version }}
      id: node-version
      uses: actions/setup-node@v4.4.0
      with:
        node-version: ${{ inputs.node-version }}
        cache: 'yarn'

    - name: Use Deno ${{ inputs.deno-version }}
      uses: denoland/setup-deno@v1
      with:
        deno-version: ${{ inputs.deno-version }}

    - name: yarn login
      shell: bash
      if: inputs.NPM_TOKEN
      run: |
        echo "//registry.npmjs.org/:_authToken=${{ inputs.NPM_TOKEN }}" > ~/.npmrc

    - name: yarn install
      if: inputs.install
      shell: bash
      run: YARN_ENABLE_HARDENED_MODE=${{ inputs.HARDENED_MODE }} yarn
